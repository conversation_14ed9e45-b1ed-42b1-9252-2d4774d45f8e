* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: #f8fafc;
  color: #1e293b;
}

.upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
  background: #f8fafc;
}

.upload-container h1 {
  color: #1e293b;
  margin-bottom: 2rem;
  font-size: 2rem;
}

.upload-area {
  border: 2px dashed #64748b;
  border-radius: 12px;
  padding: 3rem;
  text-align: center;
  margin: 1rem 0;
  background: white;
  transition: all 0.3s ease;
}

.upload-area:hover {
  border-color: #3b82f6;
  background: #f1f5f9;
}

.upload-area input[type="file"] {
  display: none;
}

.upload-area label {
  cursor: pointer;
  padding: 1rem 2rem;
  background: #3b82f6;
  color: white;
  border-radius: 8px;
  display: inline-block;
  font-weight: 500;
  transition: background 0.2s ease;
}

.upload-area label:hover {
  background: #2563eb;
}

.error {
  color: #dc2626;
  margin-top: 1rem;
  padding: 1rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
}

.app {
  max-width: 100vw;
  margin: 0;
  background: #ffffff;
  min-height: 100vh;
  color: #1e293b;
}

header {
  background: #1e293b;
  padding: 1rem 2rem;
  border-bottom: 1px solid #334155;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

header h1 {
  margin: 0;
  color: #ffffff;
  font-size: 1.5rem;
}

.metadata {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.metadata span {
  background: #475569;
  color: #ffffff;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
}

.reset-btn {
  padding: 0.75rem 1.5rem;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s ease;
}

.reset-btn:hover {
  background: #dc2626;
}

.content {
  display: flex;
  height: calc(100vh - 100px);
}

.file-sidebar {
  width: 350px;
  border-right: 1px solid #e2e8f0;
  padding: 1.5rem;
  background: #f8fafc;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.sidebar-header h2 {
  margin: 0;
  color: #1e293b;
  font-size: 1.25rem;
}

.upload-btn-container {
  position: relative;
}

.add-file-btn {
  background: #10b981;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s ease;
  border: none;
  display: inline-block;
}

.add-file-btn:hover {
  background: #059669;
}

.files-list {
  flex: 1;
  overflow-y: auto;
}

.file-item {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
}

.file-item:hover {
  border-color: #3b82f6;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.file-item.selected {
  border-color: #3b82f6;
  background: #dbeafe;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.file-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.file-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
  flex: 1;
  word-break: break-word;
}

.remove-file-btn {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  line-height: 1;
  transition: background 0.2s ease;
}

.remove-file-btn:hover {
  background: #dc2626;
}

.file-stats {
  display: flex;
  gap: 0.5rem;
}

.file-stats span {
  background: #64748b;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.file-item.selected .file-stats span {
  background: #3b82f6;
}

.no-file-selected {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  text-align: center;
  color: #64748b;
}

.no-file-selected h2 {
  color: #1e293b;
  margin-bottom: 1rem;
}

.no-file-selected p {
  font-size: 1rem;
  max-width: 400px;
}

.queries-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
  background: white;
}

.queries-content h2 {
  margin-top: 0;
  color: #1e293b;
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 0.5rem;
}

.query-section-container {
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  background: #fefefe;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.query-header {
  padding: 1.5rem;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e2e8f0;
  transition: background 0.2s ease;
}

.query-header:hover {
  background: #f8fafc;
}

.query-title h3 {
  margin: 0;
  color: #1e293b;
  font-size: 1.25rem;
  margin-bottom: 0.25rem;
}

.query-time {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.query-stats {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.questions-count {
  background: #10b981;
  color: white;
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
}

.answered-count {
  background: #10b981;
  color: white;
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
}

.unanswered-count {
  background: #f59e0b;
  color: white;
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
}

.chunks-count {
  background: #6366f1;
  color: white;
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
}

.expand-icon {
  font-size: 1.25rem;
  color: #64748b;
  margin-left: 0.5rem;
}

/* File Dashboard Styles */
.file-dashboard {
  margin-bottom: 2rem;
}

.file-tabs {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.file-tabs button {
  padding: 0.75rem 1.5rem;
  border: 2px solid #e2e8f0;
  background: white;
  color: #64748b;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.file-tabs button:hover {
  border-color: #3b82f6;
  color: #3b82f6;
}

.file-tabs button.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

/* Dashboard Summary Styles */
.dashboard-summary {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 12px;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  border: 1px solid #e2e8f0;
}

.dashboard-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  min-width: 120px;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dashboard-stat {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
}

.stat-label {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.questions-summary {
  padding: 1rem 1.5rem;
  background: #f8fafc;
}

.question-summary-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background: white;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.question-number {
  background: #3b82f6;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 2rem;
  text-align: center;
}

.question-code {
  background: #8b5cf6;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
}

.question-preview {
  flex: 1;
  color: #64748b;
  font-size: 0.875rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.query-details {
  padding: 1.5rem;
}

.tabs {
  display: flex;
  border-bottom: 2px solid #e2e8f0;
  margin-bottom: 1.5rem;
  background: #f8fafc;
  border-radius: 8px 8px 0 0;
}

.tabs button {
  padding: 1rem 1.5rem;
  border: none;
  background: none;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  color: #64748b;
  font-weight: 500;
  transition: all 0.2s ease;
}

.tabs button.active {
  border-bottom-color: #3b82f6;
  color: #3b82f6;
  background: white;
}

.tabs button:hover {
  background: #f1f5f9;
  color: #1e293b;
}

.question-item {
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  background: #fefefe;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.question-item h3, .question-item h4 {
  margin-top: 0;
  color: #1e293b;
  font-size: 1.125rem;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e2e8f0;
}

.chunk-item h4, .query-item h4 {
  margin-top: 0;
  color: #1e293b;
  font-size: 1.125rem;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e2e8f0;
}

.question-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.question-meta span {
  background: #3b82f6;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
}

.question-text, .answer-text, .answer-context, .answer-reason {
  margin-bottom: 1rem;
  line-height: 1.6;
  color: #374151;
}

.question-text strong, .answer-text strong, .answer-context strong, .answer-reason strong {
  color: #1e293b;
  font-weight: 600;
}

.answer-text {
  background: #ecfdf5;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #10b981;
}

.answer-context {
  background: #fef3c7;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #f59e0b;
}

.answer-reason {
  background: #e0e7ff;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #6366f1;
}

.raw-output pre {
  background: #1e293b;
  color: #e2e8f0;
  padding: 1.5rem;
  border-radius: 8px;
  overflow-x: auto;
  white-space: pre-wrap;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.chunk-item {
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  background: #fefefe;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.chunk-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.chunk-header h3 {
  margin: 0;
  color: #1e293b;
  font-size: 1.125rem;
}

.chunk-meta {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.chunk-meta span {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
}

.chunk-meta span:nth-child(1) {
  background: #8b5cf6;
  color: white;
}

.chunk-meta span:nth-child(2) {
  background: #06b6d4;
  color: white;
}

.chunk-meta span:nth-child(3) {
  background: #f59e0b;
  color: white;
}

.chunk-content pre {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  padding: 1.5rem;
  border-radius: 8px;
  overflow-x: auto;
  white-space: pre-wrap;
  margin: 0;
  color: #374151;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  line-height: 1.5;
}

.query-item {
  margin-bottom: 2rem;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  background: #fefefe;
}

.query-item h3 {
  color: #1e293b;
  margin-bottom: 1rem;
  font-size: 1.125rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e2e8f0;
}

.query-item pre {
  background: #1e293b;
  color: #e2e8f0;
  padding: 1.5rem;
  border-radius: 8px;
  overflow-x: auto;
  white-space: pre-wrap;
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  line-height: 1.5;
}

.expandable-section {
  margin-bottom: 1.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  background: #fefefe;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  cursor: pointer;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  transition: background 0.2s ease;
}

.section-header:hover {
  background: #f1f5f9;
}

.section-header h4 {
  margin: 0;
  color: #1e293b;
  font-size: 1.125rem;
}

.section-header .expand-icon {
  font-size: 1rem;
  color: #64748b;
  transition: transform 0.2s ease;
}

.section-content {
  padding: 0;
}

.section-content pre {
  background: #1e293b;
  color: #e2e8f0;
  padding: 1.5rem;
  border-radius: 0;
  overflow-x: auto;
  white-space: pre-wrap;
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  line-height: 1.5;
  border-top: 1px solid #334155;
}

.question-header {
  background: #f8fafc !important;
}

.question-header:hover {
  background: #f1f5f9 !important;
}

.question-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.question-title h4 {
  margin: 0;
  color: #1e293b;
  font-size: 1.125rem;
}

.question-code-badge {
  background: #3b82f6;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
}

.answered-badge {
  background: #10b981;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  margin-left: 0.5rem;
}

.unanswered-badge {
  background: #f59e0b;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  margin-left: 0.5rem;
}

.question-content {
  padding: 1.5rem;
}

.question-content .question-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.question-content .question-meta span {
  background: #3b82f6;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
}

.question-content .question-text,
.question-content .answer-text,
.question-content .answer-context,
.question-content .answer-reason {
  margin-bottom: 1rem;
  line-height: 1.6;
  color: #374151;
}

.question-content .question-text strong,
.question-content .answer-text strong,
.question-content .answer-context strong,
.question-content .answer-reason strong {
  color: #1e293b;
  font-weight: 600;
}

.question-content .answer-text {
  background: #ecfdf5;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #10b981;
}

.question-content .answer-context {
  background: #fef3c7;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #f59e0b;
}

.question-content .answer-reason {
  background: #e0e7ff;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #6366f1;
}

.question-content .raw-output pre {
  background: #1e293b;
  color: #e2e8f0;
  padding: 1.5rem;
  border-radius: 8px;
  overflow-x: auto;
  white-space: pre-wrap;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  border: none;
}

.no-chunks-message,
.no-questions-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  color: #64748b;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.no-chunks-message p,
.no-questions-message p {
  margin: 0;
  font-size: 1rem;
  color: #64748b;
}

.unanswered-notice {
  background: #fef3c7;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #f59e0b;
  margin-bottom: 1rem;
  line-height: 1.6;
  color: #374151;
}

.unanswered-notice strong {
  color: #92400e;
  font-weight: 600;
}

/* Unanswered Questions List Styles */
.unanswered-questions-list {
  background: #fefefe;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.unanswered-questions-list h3 {
  margin: 0 0 1.5rem 0;
  color: #1e293b;
  font-size: 1.25rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #f59e0b;
}

.unanswered-questions-list .question-header {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 0;
}

.unanswered-questions-list .question-header:hover {
  background: #fde68a;
}

.unanswered-questions-list .section-content {
  border: 1px solid #f59e0b;
  border-top: none;
  border-radius: 0 0 8px 8px;
  background: white;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
