import { useState } from 'react'
import './App.css'

interface RetrievalData {
  timestamp: number
  embedding_model: string
  client_id: string
  assessment_id: string
  retrieval_info: {
    retrieved_chunks: Array<{
      chunk_index: number
      content: string
      metadata: any
      question_codes?: string[]  // Optional for backward compatibility
    }>
    query: string
    llm_input: string
    llm_output: string
    retrieval_input: string
  }
}

interface RAGData {
  embedding_model: string
  client_id: string
  assessment_id: string
  timestamp: number
  total_queries: number
  retrieval_data: RetrievalData[]
}

interface UploadedFile {
  id: string
  name: string
  data: RAGData
  uploadTime: number
}

function App() {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([])
  const [selectedFileId, setSelectedFileId] = useState<string | null>(null)
  const [fileViewTab, setFileViewTab] = useState<'all' | 'answered' | 'unanswered'>('all')
  const [error, setError] = useState<string>('')

  const selectedFile = uploadedFiles.find(f => f.id === selectedFileId)
  const ragData = selectedFile?.data || null

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    setError('')
    let processedCount = 0
    let errorCount = 0
    const newFiles: UploadedFile[] = []

    Array.from(files).forEach((file, index) => {
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const jsonData = JSON.parse(e.target?.result as string)
          const newFile: UploadedFile = {
            id: `${Date.now()}-${index}`,
            name: file.name,
            data: jsonData,
            uploadTime: Date.now()
          }
          newFiles.push(newFile)
        } catch (err) {
          errorCount++
        }

        processedCount++

        // When all files are processed
        if (processedCount === files.length) {
          if (newFiles.length > 0) {
            setUploadedFiles(prev => [...prev, ...newFiles])
            setSelectedFileId(newFiles[0].id)
            setFileViewTab('all')
          }

          if (errorCount > 0) {
            setError(`${errorCount} file(s) failed to upload. Please ensure all files are valid JSON.`)
          }

          // Reset file input
          event.target.value = ''
        }
      }
      reader.readAsText(file)
    })
  }

  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId))
    if (selectedFileId === fileId) {
      const remaining = uploadedFiles.filter(f => f.id !== fileId)
      setSelectedFileId(remaining.length > 0 ? remaining[0].id : null)
    }
  }

  const formatFileTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString()
  }

  // Helper function to check if a question is answered
  const isQuestionAnswered = (question: any) => {
    return question.answer_text &&
           question.answer_text !== 'Not Available' &&
           question.answer_text !== 'not available' &&
           question.answer_text !== ''
  }

  // Helper function to parse questions from LLM output
  const parseQuestions = (llmOutput: string) => {
    try {
      if (!llmOutput) {
        return [{ raw_output: 'No LLM output available' }]
      }

      let output = llmOutput
      // Remove markdown code blocks if present
      if (output.includes('```json')) {
        output = output.replace(/```json\s*/, '').replace(/```\s*$/, '')
      }
      const parsed = JSON.parse(output)
      return Array.isArray(parsed) ? parsed : [parsed]
    } catch {
      return [{ raw_output: llmOutput || 'No LLM output available' }]
    }
  }

  // Calculate file-level statistics
  const calculateFileStats = (ragData: RAGData) => {
    let totalQuestions = 0
    let totalAnswered = 0
    let totalUnanswered = 0
    let totalChunks = 0

    ragData.retrieval_data.forEach(session => {
      const questions = parseQuestions(session.retrieval_info.llm_output)
      totalQuestions += questions.length

      const answered = questions.filter(isQuestionAnswered)
      totalAnswered += answered.length
      totalUnanswered += questions.length - answered.length

      totalChunks += session.retrieval_info.retrieved_chunks ? session.retrieval_info.retrieved_chunks.length : 0
    })

    return {
      totalQuestions,
      totalAnswered,
      totalUnanswered,
      totalChunks,
      answerRate: totalQuestions > 0 ? Math.round((totalAnswered / totalQuestions) * 100) : 0
    }
  }



  if (uploadedFiles.length === 0) {
    return (
      <div className="upload-container">
        <h1>RAG Performance Viewer</h1>
        <div className="upload-area">
          <input
            type="file"
            accept=".json"
            onChange={handleFileUpload}
            id="file-upload"
            multiple
          />
          <label htmlFor="file-upload">
            Choose JSON files (multiple selection supported)
          </label>
        </div>
        {error && <div className="error">{error}</div>}
      </div>
    )
  }

  return (
    <div className="app">
      <header>
        <h1>RAG Performance Analysis</h1>
        {ragData && (
          <div className="metadata">
            <span>Client: {ragData.client_id}</span>
            <span>Assessment: {ragData.assessment_id}</span>
            <span>Total Queries: {ragData.total_queries}</span>
            <span>Model: {ragData.embedding_model}</span>
          </div>
        )}
        <button onClick={() => {
          setUploadedFiles([])
          setSelectedFileId(null)
        }} className="reset-btn">
          Clear All Files
        </button>
      </header>

      <div className="content">
        <div className="file-sidebar">
          <div className="sidebar-header">
            <h2>Files ({uploadedFiles.length})</h2>
            <div className="upload-btn-container">
              <input
                type="file"
                accept=".json"
                onChange={handleFileUpload}
                id="add-file-upload"
                style={{ display: 'none' }}
                multiple
              />
              <label htmlFor="add-file-upload" className="add-file-btn">
                + Add Files
              </label>
            </div>
          </div>

          <div className="files-list">
            {uploadedFiles.map((file) => (
              <div
                key={file.id}
                className={`file-item ${selectedFileId === file.id ? 'selected' : ''}`}
                onClick={() => {
                  setSelectedFileId(file.id)
                  setFileViewTab('all')
                }}
              >
                <div className="file-header">
                  <div className="file-name">{file.name}</div>
                  <button
                    className="remove-file-btn"
                    onClick={(e) => {
                      e.stopPropagation()
                      removeFile(file.id)
                    }}
                  >
                    ×
                  </button>
                </div>
                <div className="file-stats">
                  <span>{file.data.retrieval_data.length} Queries</span>
                  <span>{formatFileTime(file.uploadTime)}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {ragData ? (
          <div className="queries-content">
            <h2>All Queries ({ragData.retrieval_data.length})</h2>

            {/* File-level Dashboard */}
            <div className="file-dashboard">
              <div className="dashboard-summary">
                <div className="dashboard-card">
                  <div className="dashboard-stat">
                    <span className="stat-number">{calculateFileStats(ragData).totalAnswered}</span>
                    <span className="stat-label">Answered</span>
                  </div>
                </div>
                <div className="dashboard-card">
                  <div className="dashboard-stat">
                    <span className="stat-number">{calculateFileStats(ragData).totalUnanswered}</span>
                    <span className="stat-label">Unanswered</span>
                  </div>
                </div>
                <div className="dashboard-card">
                  <div className="dashboard-stat">
                    <span className="stat-number">{calculateFileStats(ragData).totalQuestions}</span>
                    <span className="stat-label">Total Questions</span>
                  </div>
                </div>
                <div className="dashboard-card">
                  <div className="dashboard-stat">
                    <span className="stat-number">{calculateFileStats(ragData).totalChunks}</span>
                    <span className="stat-label">Total Chunks</span>
                  </div>
                </div>
                <div className="dashboard-card">
                  <div className="dashboard-stat">
                    <span className="stat-number">{calculateFileStats(ragData).answerRate}%</span>
                    <span className="stat-label">Answer Rate</span>
                  </div>
                </div>
              </div>

              {/* File-level Filter Tabs */}
              <div className="file-tabs">
                <button
                  className={fileViewTab === 'all' ? 'active' : ''}
                  onClick={() => setFileViewTab('all')}
                >
                  All Queries ({ragData.retrieval_data.length})
                </button>
                <button
                  className={fileViewTab === 'answered' ? 'active' : ''}
                  onClick={() => setFileViewTab('answered')}
                >
                  With Answered Questions ({ragData.retrieval_data.filter(session => {
                    const questions = parseQuestions(session.retrieval_info.llm_output)
                    return questions.some(isQuestionAnswered)
                  }).length})
                </button>
                <button
                  className={fileViewTab === 'unanswered' ? 'active' : ''}
                  onClick={() => setFileViewTab('unanswered')}
                >
                  With Unanswered Questions ({ragData.retrieval_data.filter(session => {
                    const questions = parseQuestions(session.retrieval_info.llm_output)
                    return questions.some(q => !isQuestionAnswered(q))
                  }).length})
                </button>
              </div>
            </div>

            {ragData.retrieval_data
              .filter(session => {
                if (fileViewTab === 'all') return true
                const questions = parseQuestions(session.retrieval_info.llm_output)
                if (fileViewTab === 'answered') {
                  return questions.some(isQuestionAnswered)
                }
                if (fileViewTab === 'unanswered') {
                  return questions.some(q => !isQuestionAnswered(q))
                }
                return true
              })
              .map((session, index) => (
                <QuerySection
                  key={index}
                  session={session}
                  queryIndex={ragData.retrieval_data.indexOf(session)}
                  parseQuestions={parseQuestions}
                  isQuestionAnswered={isQuestionAnswered}
                />
              ))}
          </div>
        ) : (
          <div className="queries-content">
            <div className="no-file-selected">
              <h2>Select a file to view queries</h2>
              <p>Choose a file from the sidebar to analyze its RAG performance data.</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

interface QuerySectionProps {
  session: RetrievalData
  queryIndex: number
  parseQuestions: (llmOutput: string) => any[]
  isQuestionAnswered: (question: any) => boolean
}

function QuerySection({ session, queryIndex, parseQuestions, isQuestionAnswered }: QuerySectionProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [activeTab, setActiveTab] = useState<'questions' | 'chunks' | 'query' | 'unanswered'>('questions')
  const [expandedSections, setExpandedSections] = useState<{[key: string]: boolean}>({
    query: true,
    retrieverInput: false,
    llmInput: false,
    llmOutput: false
  })

  const [expandedQuestions, setExpandedQuestions] = useState<{[key: number]: boolean}>({})

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const toggleQuestion = (questionIndex: number) => {
    setExpandedQuestions(prev => ({
      ...prev,
      [questionIndex]: !prev[questionIndex]
    }))
  }

  const questions = parseQuestions(session.retrieval_info.llm_output)

  // Separate answered and unanswered questions
  const answeredQuestions = questions.filter(isQuestionAnswered)
  const unansweredQuestions = questions.filter(q => !isQuestionAnswered(q))

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString()
  }

  const getQuestionCodes = () => {
    const codes = questions
      .map(q => q.question_code)
      .filter(code => code && code !== 'N/A')
    return codes.length > 0 ? codes.join(', ') : 'No codes'
  }

  return (
    <div className="query-section-container">
      <div className="query-header" onClick={() => setIsExpanded(!isExpanded)}>
        <div className="query-title">
          <h3>Query {queryIndex + 1} ({getQuestionCodes()})</h3>
          <span className="query-time">{formatTimestamp(session.timestamp)}</span>
        </div>
        <div className="query-stats">
          <span className="questions-count">{questions.length} Questions</span>
          <span className="chunks-count">
            {session.retrieval_info.retrieved_chunks ? session.retrieval_info.retrieved_chunks.length : 0} Chunks
          </span>
          <span className="expand-icon">{isExpanded ? '▼' : '▶'}</span>
        </div>
      </div>



      {isExpanded && (
        <div className="query-details">
          <div className="tabs">
            <button
              className={activeTab === 'questions' ? 'active' : ''}
              onClick={() => setActiveTab('questions')}
            >
              Answered Questions ({answeredQuestions.length})
            </button>
            <button
              className={activeTab === 'unanswered' ? 'active' : ''}
              onClick={() => setActiveTab('unanswered')}
            >
              Unanswered Questions ({unansweredQuestions.length})
            </button>
            <button
              className={activeTab === 'chunks' ? 'active' : ''}
              onClick={() => setActiveTab('chunks')}
            >
              Retrieved Chunks ({session.retrieval_info.retrieved_chunks ? session.retrieval_info.retrieved_chunks.length : 0})
            </button>
            <button
              className={activeTab === 'query' ? 'active' : ''}
              onClick={() => setActiveTab('query')}
            >
              Query & Input
            </button>
          </div>

          <div className="tab-content">
            {activeTab === 'questions' && (
              <div className="questions-section">
                {answeredQuestions.length > 0 ? (
                  answeredQuestions.map((q) => {
                    const originalIndex = questions.findIndex(question => question === q)
                    return (
                      <div key={originalIndex} className="expandable-section">
                        <div
                          className="section-header question-header"
                          onClick={() => toggleQuestion(originalIndex)}
                        >
                          <div className="question-title">
                            <h4>Question {originalIndex + 1}</h4>
                            {q.question_code && (
                              <span className="question-code-badge">{q.question_code}</span>
                            )}
                            <span className="answered-badge">✓ Answered</span>
                          </div>
                          <span className="expand-icon">{expandedQuestions[originalIndex] ? '▼' : '▶'}</span>
                        </div>

                        {expandedQuestions[originalIndex] && (
                          <div className="section-content question-content">
                            {q.question_code && (
                              <div className="question-meta">
                                <span><strong>Code:</strong> {q.question_code}</span>
                                {q.question_type && <span><strong>Type:</strong> {q.question_type}</span>}
                              </div>
                            )}
                            {q.question_text && (
                              <div className="question-text">
                                <strong>Question:</strong> {q.question_text}
                              </div>
                            )}
                            {q.answer_text && (
                              <div className="answer-text">
                                <strong>Answer:</strong> {Array.isArray(q.answer_text) ? q.answer_text.join(', ') : q.answer_text}
                              </div>
                            )}
                            {q.answer_context && (
                              <div className="answer-context">
                                <strong>Context:</strong> {Array.isArray(q.answer_context) ? q.answer_context.join(', ') : q.answer_context}
                              </div>
                            )}
                            {q.answer_reason && (
                              <div className="answer-reason">
                                <strong>Reasoning:</strong> {Array.isArray(q.answer_reason) ? q.answer_reason.join(' ') : q.answer_reason}
                              </div>
                            )}
                            {q.confidence_score && (
                              <div className="answer-reason">
                                <strong>Confidence Score:</strong> {Array.isArray(q.confidence_score) ? q.confidence_score.join(' ') : q.confidence_score}
                              </div>
                            )}
                            {q.raw_output && (
                              <div className="raw-output">
                                <strong>Raw Output:</strong>
                                <pre>{q.raw_output}</pre>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    )
                  })
                ) : (
                  <div className="no-questions-message">
                    <p>No answered questions found in this query.</p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'unanswered' && (
              <div className="questions-section">
                {unansweredQuestions.length > 0 ? (
                  unansweredQuestions.map((q) => {
                    const originalIndex = questions.findIndex(question => question === q)
                    return (
                      <div key={originalIndex} className="expandable-section">
                        <div
                          className="section-header question-header"
                          onClick={() => toggleQuestion(originalIndex)}
                        >
                          <div className="question-title">
                            <h4>Question {originalIndex + 1}</h4>
                            {q.question_code && (
                              <span className="question-code-badge">{q.question_code}</span>
                            )}
                            <span className="unanswered-badge">⚠ Unanswered</span>
                          </div>
                          <span className="expand-icon">{expandedQuestions[originalIndex] ? '▼' : '▶'}</span>
                        </div>

                        {expandedQuestions[originalIndex] && (
                          <div className="section-content question-content">
                            {q.question_code && (
                              <div className="question-meta">
                                <span><strong>Code:</strong> {q.question_code}</span>
                                {q.question_type && <span><strong>Type:</strong> {q.question_type}</span>}
                              </div>
                            )}
                            {q.question_text && (
                              <div className="question-text">
                                <strong>Question:</strong> {q.question_text}
                              </div>
                            )}
                            <div className="unanswered-notice">
                              <strong>Status:</strong> This question was not answered or the answer was marked as "Not Available"
                            </div>
                            {q.answer_context && (
                              <div className="answer-context">
                                <strong>Context:</strong> {Array.isArray(q.answer_context) ? q.answer_context.join(', ') : q.answer_context}
                              </div>
                            )}
                            {q.answer_reason && (
                              <div className="answer-reason">
                                <strong>Reasoning:</strong> {Array.isArray(q.answer_reason) ? q.answer_reason.join(' ') : q.answer_reason}
                              </div>
                            )}
                            {q.raw_output && (
                              <div className="raw-output">
                                <strong>Raw Output:</strong>
                                <pre>{q.raw_output}</pre>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    )
                  })
                ) : (
                  <div className="no-questions-message">
                    <p>All questions in this query have been answered! 🎉</p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'chunks' && (
              <div className="chunks-section">
                {session.retrieval_info.retrieved_chunks ? (
                  session.retrieval_info.retrieved_chunks.map((chunk, idx) => (
                    <div key={idx} className="chunk-item">
                      <div className="chunk-header">
                        <h4>Chunk {chunk.chunk_index}</h4>
                        <div className="chunk-meta">
                          <span>Type: {chunk.metadata?.chunk_type || 'default_text_split'}</span>
                          {chunk.metadata?.relative_timing && (
                            <span>Timing: {chunk.metadata.relative_timing}</span>
                          )}
                          {chunk.metadata?.total_chunks && (
                            <span>Total: {chunk.metadata.total_chunks}</span>
                          )}
                          {chunk.question_codes && chunk.question_codes.length > 0 && (
                            <span>OASIS: {chunk.question_codes.join(', ')}</span>
                          )}
                        </div>
                      </div>
                      <div className="chunk-content">
                        <pre>{chunk.content || 'No content available'}</pre>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="no-chunks-message">
                    <p>No retrieved chunks available for this query.</p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'query' && (
              <div className="query-section">
                <div className="expandable-section">
                  <div
                    className="section-header"
                    onClick={() => toggleSection('query')}
                  >
                    <h4>Original Query</h4>
                    <span className="expand-icon">{expandedSections.query ? '▼' : '▶'}</span>
                  </div>
                  {expandedSections.query && (
                    <div className="section-content">
                      <pre>{session.retrieval_info.query || 'No query available'}</pre>
                    </div>
                  )}
                </div>

                <div className="expandable-section">
                  <div
                    className="section-header"
                    onClick={() => toggleSection('retrieverInput')}
                  >
                    <h4>Retriever Input</h4>
                    <span className="expand-icon">{expandedSections.retrieverInput ? '▼' : '▶'}</span>
                  </div>
                  {expandedSections.retrieverInput && (
                    <div className="section-content">
                      <pre>{session.retrieval_info.retrieval_input || 'No retriever input available'}</pre>
                    </div>
                  )}
                </div>

                <div className="expandable-section">
                  <div
                    className="section-header"
                    onClick={() => toggleSection('llmInput')}
                  >
                    <h4>LLM Input</h4>
                    <span className="expand-icon">{expandedSections.llmInput ? '▼' : '▶'}</span>
                  </div>
                  {expandedSections.llmInput && (
                    <div className="section-content">
                      <pre>{session.retrieval_info.llm_input || 'No LLM input available'}</pre>
                    </div>
                  )}
                </div>

                <div className="expandable-section">
                  <div
                    className="section-header"
                    onClick={() => toggleSection('llmOutput')}
                  >
                    <h4>LLM Output</h4>
                    <span className="expand-icon">{expandedSections.llmOutput ? '▼' : '▶'}</span>
                  </div>
                  {expandedSections.llmOutput && (
                    <div className="section-content">
                      <pre>{session.retrieval_info.llm_output || 'No LLM output available'}</pre>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default App
